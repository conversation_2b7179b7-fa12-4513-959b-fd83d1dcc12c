import { RowDataPacket } from "mysql2";

// Enums
export enum RoomType {
    STANDARD = 'Standard',
    VIP = 'VIP',
    PREMIUM = 'Premium',
    SUITE = 'Suite'
}

export enum RoomStatus {
    AVAILABLE = 'available',
    OCCUPIED = 'occupied',
    MAINTENANCE = 'maintenance',
    OUT_OF_ORDER = 'out_of_order'
}

// Value Objects
export class Money {
    private amount: number;
    private currency: string;

    constructor(amount: number, currency: string = 'VND') {
        if (amount < 0) {
            throw new Error('Amount cannot be negative');
        }
        this.amount = amount;
        this.currency = currency;
    }

    getAmount(): number {
        return this.amount;
    }

    getCurrency(): string {
        return this.currency;
    }

    add(other: Money): Money {
        if (this.currency !== other.currency) {
            throw new Error('Cannot add different currencies');
        }
        return new Money(this.amount + other.amount, this.currency);
    }

    subtract(other: Money): Money {
        if (this.currency !== other.currency) {
            throw new Error('Cannot subtract different currencies');
        }
        return new Money(this.amount - other.amount, this.currency);
    }

    multiply(factor: number): Money {
        return new Money(this.amount * factor, this.currency);
    }

    divide(divisor: number): Money {
        if (divisor === 0) {
            throw new Error('Cannot divide by zero');
        }
        return new Money(this.amount / divisor, this.currency);
    }

    isZero(): boolean {
        return this.amount === 0;
    }

    isPositive(): boolean {
        return this.amount > 0;
    }

    equals(other: Money): boolean {
        return this.amount === other.amount && this.currency === other.currency;
    }

    toString(): string {
        return `${this.amount.toLocaleString()} ${this.currency}`;
    }

    format(): string {
        return `${this.amount.toLocaleString()}đ`;
    }
}

// Validation Result
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
}

// Room Class
export class Room {
    private id: number;
    private name: string;
    private type: RoomType;
    private pricePerHour: Money;
    private capacity: number;
    private description: string;
    private status: RoomStatus;
    private createdAt: Date;
    private updatedAt: Date;

    constructor(
        id: number,
        name: string,
        type: RoomType,
        pricePerHour: number,
        capacity: number,
        description: string = '',
        status: RoomStatus = RoomStatus.AVAILABLE
    ) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.pricePerHour = new Money(pricePerHour);
        this.capacity = capacity;
        this.description = description;
        this.status = status;
        this.createdAt = new Date();
        this.updatedAt = new Date();
    }

    // Getters
    getId(): number {
        return this.id;
    }

    getName(): string {
        return this.name;
    }

    getType(): RoomType {
        return this.type;
    }

    getPricePerHour(): Money {
        return this.pricePerHour;
    }

    getCapacity(): number {
        return this.capacity;
    }

    getDescription(): string {
        return this.description;
    }

    getStatus(): RoomStatus {
        return this.status;
    }

    getCreatedAt(): Date {
        return this.createdAt;
    }

    getUpdatedAt(): Date {
        return this.updatedAt;
    }

    // Setters
    setName(name: string): void {
        if (!name || name.trim().length === 0) {
            throw new Error('Room name cannot be empty');
        }
        this.name = name.trim();
        this.updateTimestamp();
    }

    setType(type: RoomType): void {
        this.type = type;
        this.updateTimestamp();
    }

    setPricePerHour(price: number): void {
        this.pricePerHour = new Money(price);
        this.updateTimestamp();
    }

    setCapacity(capacity: number): void {
        if (capacity <= 0) {
            throw new Error('Capacity must be greater than 0');
        }
        this.capacity = capacity;
        this.updateTimestamp();
    }

    setDescription(description: string): void {
        this.description = description;
        this.updateTimestamp();
    }

    setStatus(status: RoomStatus): void {
        this.status = status;
        this.updateTimestamp();
    }

    // Business Logic Methods
    isAvailable(): boolean {
        return this.status === RoomStatus.AVAILABLE;
    }

    isOccupied(): boolean {
        return this.status === RoomStatus.OCCUPIED;
    }

    isMaintenance(): boolean {
        return this.status === RoomStatus.MAINTENANCE;
    }

    calculateCost(hours: number): Money {
        if (hours <= 0) {
            throw new Error('Hours must be greater than 0');
        }
        return this.pricePerHour.multiply(hours);
    }

    canAccommodate(guestCount: number): boolean {
        return guestCount <= this.capacity;
    }

    validate(): ValidationResult {
        const errors: string[] = [];

        if (!this.name || this.name.trim().length === 0) {
            errors.push('Room name is required');
        }

        if (this.capacity <= 0) {
            errors.push('Capacity must be greater than 0');
        }

        if (!this.pricePerHour.isPositive()) {
            errors.push('Price per hour must be greater than 0');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    equals(other: Room): boolean {
        return this.id === other.id;
    }

    toString(): string {
        return `Room[${this.id}]: ${this.name} (${this.type}) - ${this.pricePerHour.format()}/hour - Capacity: ${this.capacity}`;
    }

    toJSON(): object {
        return {
            id: this.id,
            name: this.name,
            type: this.type,
            price_per_hour: this.pricePerHour.getAmount(),
            capacity: this.capacity,
            description: this.description,
            status: this.status,
            created_at: this.createdAt,
            updated_at: this.updatedAt
        };
    }

    private updateTimestamp(): void {
        this.updatedAt = new Date();
    }

    // Static Factory Methods
    static fromJSON(data: any): Room {
        return new Room(
            data.id,
            data.name,
            data.type as RoomType,
            data.price_per_hour,
            data.capacity,
            data.description || '',
            data.status as RoomStatus || RoomStatus.AVAILABLE
        );
    }

    static create(
        name: string,
        type: RoomType,
        pricePerHour: number,
        capacity: number,
        description?: string
    ): Room {
        return new Room(0, name, type, pricePerHour, capacity, description);
    }
}

// Legacy interfaces for backward compatibility
export interface RoomData {
    id: number;
    name: string;
    type: RoomType;
    price_per_hour: number;
    capacity: number;
    description?: string;
    status?: RoomStatus;
    created_at: Date;
    updated_at: Date;
}

export interface RoomRow extends RoomData, RowDataPacket {}