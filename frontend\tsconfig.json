{
  "compilerOptions": {
    "target": "es6",
    "module": "esnext",
    "jsx": "react-jsx",
    "jsxImportSource": "react",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "outDir": "./dist",
    "baseUrl": "./src",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "noFallthroughCasesInSwitch": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true
  },
  "include": [
    "src/**/*"  // <PERSON>o gồm tất cả files trong thư mục src
  ],
  "exclude": [
    "node_modules",  // Loại trừ thư mục node_modules
    "dist"  // <PERSON><PERSON><PERSON> trừ thư mục dist
  ]
}
