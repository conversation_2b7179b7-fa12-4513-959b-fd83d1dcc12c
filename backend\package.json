{"name": "karaoke-management", "version": "1.0.0", "description": "A Node.js web service for managing karaoke rooms and bookings.", "main": "src/server.ts", "scripts": {"start": "ts-node src/server.ts", "dev": "nodemon --exec ts-node src/server.ts", "build": "tsc", "test": "jest"}, "dependencies": {"@types/cors": "^2.8.17", "axios": "^1.9.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.17.1", "jsonwebtoken": "^9.0.2", "mysql2": "^2.3.3", "sequelize": "^6.6.5"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/dotenv": "^6.1.1", "@types/express": "^4.17.21", "@types/jest": "^27.0.2", "@types/jsonwebtoken": "^9.0.9", "jest": "^27.0.6", "nodemon": "^3.1.10", "ts-node": "^10.4.0", "typescript": "^4.4.4"}, "author": "Your Name", "license": "MIT"}