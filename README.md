# # KaraokeManagerment

KaraokeManagerment is a system designed to simplify the management of karaoke rooms, customers, and bookings. It includes both backend and frontend components for a seamless user experience.
## Features

- Customer Management: Manage customer profiles.
- Room Management: Add, update, and view room details.
- Booking Management: Create and track karaoke room bookings.
- Revenue Reports: Generate reports on revenue and room usage.
## Project Structure

- `backend`: Contains the server-side code built with Node.js and Express.
- `frontend`: Contains the client-side code built with React.
## Installation

1. Clone the repository:
   ```
   git clone https://github.com/LePhanNham/KaraokeManagerment.git
   ```

2. Install dependencies for the backend and frontend:
   ```
   cd backend
   npm install
   cd ../frontend
   npm install
   ```

3. Start the backend server:
   ```
   npm start
   ```

4. Start the frontend development server:
   ```
   npm start
   ```

The backend will run on `http://localhost:3000`, and the frontend will run on `http://localhost:3001` (or similar).## Contributing

Contributions are welcome! Please open an issue or create a pull request for any improvements or suggestions.
## License

This project is licensed under the MIT License.
