{"name": "karaoke-management", "version": "1.0.0", "private": true, "description": "A karaoke management system built with React.", "main": "src/index.tsx", "scripts": {"start": "react-scripts start", "build": "react-scripts build"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.17.1", "@types/axios": "^0.9.36", "axios": "^1.9.0", "date-fns": "^4.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.11.2", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "recharts": "^2.15.3"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-router-dom": "^5.3.0", "@types/recharts": "^1.8.29"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version"]}}