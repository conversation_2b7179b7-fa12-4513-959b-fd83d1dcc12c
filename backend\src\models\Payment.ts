import { Money, ValidationResult } from './Room';

// Enums
export enum PaymentMethod {
    CASH = 'cash',
    CARD = 'card',
    TRANSFER = 'transfer',
    E_WALLET = 'e_wallet'
}

export enum PaymentStatus {
    PENDING = 'pending',
    COMPLETED = 'completed',
    CANCELLED = 'cancelled',
    REFUNDED = 'refunded',
    FAILED = 'failed'
}

// Payment Class
export class Payment {
    private id: number;
    private bookingId?: number;
    private bookingGroupId?: number;
    private bookingRoomId?: number;
    private customerId: number;
    private amount: Money;
    private paymentMethod: PaymentMethod;
    private status: PaymentStatus;
    private transactionId?: string;
    private paymentDate: Date;
    private notes: string;
    private createdAt: Date;
    private updatedAt: Date;

    constructor(
        id: number,
        customerId: number,
        amount: number,
        paymentMethod: PaymentMethod,
        bookingId?: number,
        bookingGroupId?: number,
        bookingRoomId?: number
    ) {
        this.id = id;
        this.customerId = customerId;
        this.amount = new Money(amount);
        this.paymentMethod = paymentMethod;
        this.bookingId = bookingId;
        this.bookingGroupId = bookingGroupId;
        this.bookingRoomId = bookingRoomId;
        this.status = PaymentStatus.PENDING;
        this.paymentDate = new Date();
        this.notes = '';
        this.createdAt = new Date();
        this.updatedAt = new Date();
    }

    // Getters
    getId(): number {
        return this.id;
    }

    getBookingId(): number | undefined {
        return this.bookingId;
    }

    getBookingGroupId(): number | undefined {
        return this.bookingGroupId;
    }

    getBookingRoomId(): number | undefined {
        return this.bookingRoomId;
    }

    getCustomerId(): number {
        return this.customerId;
    }

    getAmount(): Money {
        return this.amount;
    }

    getPaymentMethod(): PaymentMethod {
        return this.paymentMethod;
    }

    getStatus(): PaymentStatus {
        return this.status;
    }

    getTransactionId(): string | undefined {
        return this.transactionId;
    }

    getPaymentDate(): Date {
        return this.paymentDate;
    }

    getNotes(): string {
        return this.notes;
    }

    getCreatedAt(): Date {
        return this.createdAt;
    }

    getUpdatedAt(): Date {
        return this.updatedAt;
    }

    // Setters
    setAmount(amount: number): void {
        this.amount = new Money(amount);
        this.updateTimestamp();
    }

    setPaymentMethod(method: PaymentMethod): void {
        if (this.status !== PaymentStatus.PENDING) {
            throw new Error('Cannot change payment method after processing');
        }
        this.paymentMethod = method;
        this.updateTimestamp();
    }

    setStatus(status: PaymentStatus): void {
        this.status = status;
        this.updateTimestamp();
    }

    setTransactionId(transactionId: string): void {
        this.transactionId = transactionId;
        this.updateTimestamp();
    }

    setNotes(notes: string): void {
        this.notes = notes;
        this.updateTimestamp();
    }

    // Business Logic Methods
    isPending(): boolean {
        return this.status === PaymentStatus.PENDING;
    }

    isCompleted(): boolean {
        return this.status === PaymentStatus.COMPLETED;
    }

    isCancelled(): boolean {
        return this.status === PaymentStatus.CANCELLED;
    }

    isRefunded(): boolean {
        return this.status === PaymentStatus.REFUNDED;
    }

    isFailed(): boolean {
        return this.status === PaymentStatus.FAILED;
    }

    canBeRefunded(): boolean {
        return this.status === PaymentStatus.COMPLETED;
    }

    canBeCancelled(): boolean {
        return this.status === PaymentStatus.PENDING;
    }

    process(): void {
        if (!this.isPending()) {
            throw new Error('Payment can only be processed when pending');
        }
        this.status = PaymentStatus.COMPLETED;
        this.paymentDate = new Date();
        this.updateTimestamp();
    }

    cancel(reason?: string): void {
        if (!this.canBeCancelled()) {
            throw new Error('Payment cannot be cancelled in current status');
        }
        this.status = PaymentStatus.CANCELLED;
        if (reason) {
            this.notes = `Cancelled: ${reason}`;
        }
        this.updateTimestamp();
    }

    refund(refundAmount?: number, reason?: string): void {
        if (!this.canBeRefunded()) {
            throw new Error('Payment cannot be refunded in current status');
        }

        if (refundAmount && refundAmount > this.amount.getAmount()) {
            throw new Error('Refund amount cannot exceed payment amount');
        }

        this.status = PaymentStatus.REFUNDED;
        if (reason) {
            this.notes = `Refunded: ${reason}`;
        }
        this.updateTimestamp();
    }

    validate(): ValidationResult {
        const errors: string[] = [];

        if (this.customerId <= 0) {
            errors.push('Customer ID is required');
        }

        if (!this.amount.isPositive()) {
            errors.push('Payment amount must be greater than 0');
        }

        if (!this.bookingId && !this.bookingGroupId && !this.bookingRoomId) {
            errors.push('Payment must be associated with a booking, booking group, or booking room');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    equals(other: Payment): boolean {
        return this.id === other.id;
    }

    toString(): string {
        return `Payment[${this.id}]: ${this.amount.format()} via ${this.paymentMethod} - Status: ${this.status}`;
    }

    toJSON(): object {
        return {
            id: this.id,
            booking_id: this.bookingId,
            booking_group_id: this.bookingGroupId,
            booking_room_id: this.bookingRoomId,
            customer_id: this.customerId,
            amount: this.amount.getAmount(),
            payment_method: this.paymentMethod,
            status: this.status,
            transaction_id: this.transactionId,
            payment_date: this.paymentDate,
            notes: this.notes,
            created_at: this.createdAt,
            updated_at: this.updatedAt
        };
    }

    private updateTimestamp(): void {
        this.updatedAt = new Date();
    }

    // Static Factory Methods
    static fromJSON(data: any): Payment {
        const payment = new Payment(
            data.id,
            data.customer_id,
            data.amount,
            data.payment_method as PaymentMethod,
            data.booking_id,
            data.booking_group_id,
            data.booking_room_id
        );

        if (data.status) {
            payment.setStatus(data.status as PaymentStatus);
        }
        if (data.transaction_id) {
            payment.setTransactionId(data.transaction_id);
        }
        if (data.notes) {
            payment.setNotes(data.notes);
        }
        if (data.payment_date) {
            payment.paymentDate = new Date(data.payment_date);
        }
        if (data.created_at) {
            payment.createdAt = new Date(data.created_at);
        }
        if (data.updated_at) {
            payment.updatedAt = new Date(data.updated_at);
        }

        return payment;
    }

    static create(
        customerId: number,
        amount: number,
        paymentMethod: PaymentMethod,
        bookingId?: number,
        bookingGroupId?: number,
        bookingRoomId?: number
    ): Payment {
        return new Payment(0, customerId, amount, paymentMethod, bookingId, bookingGroupId, bookingRoomId);
    }
}

// Legacy interface for backward compatibility
export interface PaymentData {
    id?: number;
    booking_id?: number;
    booking_group_id?: number;
    booking_room_id?: number;
    customer_id: number;
    amount: number;
    payment_method: string;
    status?: string;
    transaction_id?: string;
    payment_date: Date;
    notes?: string;
    created_at?: Date;
    updated_at?: Date;
}
